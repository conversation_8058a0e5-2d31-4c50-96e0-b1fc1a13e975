# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-token

# Redis Configuration (fallback)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password
KV_URL=redis://localhost:6379
KV_REST_API_TOKEN=your-kv-token

# Cache Configuration
CACHING_ENABLED=true
CACHE_DEFAULT_TTL=300
MAX_CACHE_SIZE=100
REDIS_MAX_RETRIES=3
REDIS_FALLBACK_ENABLED=true
USE_UPSTASH_REDIS=true

# Rate Limiting Configuration
RATE_LIMITING_ENABLED=true
ANONYMOUS_RATE_LIMIT=100
AUTHENTICATED_RATE_LIMIT=1000

# Performance Monitoring
METRICS_ENABLED=true
LOG_LEVEL=info
ERROR_RATE_THRESHOLD=5
RESPONSE_TIME_THRESHOLD=1000
CACHE_HIT_THRESHOLD=0.8
ERROR_ALERT_DURATION=300000
RESPONSE_TIME_DURATION=300000
CACHE_HIT_DURATION=600000

# Security Configuration
ALLOWED_ORIGINS=*
MAX_REQUEST_SIZE=1mb
API_TIMEOUT=30000

# Timezone Configuration
TIMEZONE_DATA_SOURCE=iana
TIMEZONE_UPDATE_INTERVAL=86400000
FALLBACK_TIMEZONE=UTC
MAX_TIMEZONE_OFFSET=43200

# Environment
NODE_ENV=development