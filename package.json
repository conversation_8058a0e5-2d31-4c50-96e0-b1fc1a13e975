{"name": "timestamp-converter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:api": "bash scripts/start-dev.sh", "dev:full": "concurrently \"npm run dev\" \"npm run dev:api\"", "build": "tsc --noEmit && vite build", "build:frontend": "tsc && vite build", "build:api": "tsc -p api/tsconfig.json", "build:cloudflare": "tsc --noEmit && vite build", "deploy:cloudflare": "npm run build:cloudflare && wrangler pages deploy dist", "preview:cloudflare": "wrangler pages dev dist", "test:migration": "node scripts/test-migration.js", "verify:migration": "node scripts/verify-migration.js", "test:api-docs": "node scripts/test-api-docs.js", "monitor:production": "node scripts/monitor-production.js", "test:cache": "node scripts/test-cache.js", "test:production": "node scripts/test-production-optimization.js", "test:api-extensions": "node scripts/test-api-extensions.js", "test:frontend": "node scripts/test-frontend-features.js", "test:unified": "node scripts/test-unified-handlers.js", "typescript:analyze": "node scripts/analyze-typescript.js", "typescript:optimize": "node scripts/optimize-typescript.js", "typescript:fix": "node scripts/fix-typescript-issues.js", "typescript:quickfix": "node scripts/quick-fix-typescript.js", "verify:refactoring": "node scripts/verify-refactoring.js", "verify:implementation": "node scripts/verify-implementation.js", "fix:dark-mode": "node scripts/fix-dark-mode-styles.js", "fix:accessibility": "node scripts/fix-accessibility.js", "fix:dark-mode-comprehensive": "node scripts/fix-dark-mode-comprehensive.js", "fix:color-scheme": "node scripts/fix-color-scheme-and-dropdowns.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:api": "node scripts/test-apis.js", "test:working": "node test-working-apis.js", "audit": "npm audit", "audit:fix": "npm audit fix", "security:check": "npm audit --audit-level high"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@upstash/redis": "^1.35.1", "@vercel/node": "^2.3.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "concurrently": "^9.2.0", "cors": "^2.8.5", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "express": "^4.21.2", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vercel": "^44.5.0", "vite": "^4.5.3", "vitest": "^1.0.4", "wrangler": "^4.26.0"}, "engines": {"node": ">=18.0.0"}}